import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { EmailService } from '../../../shared/services/email.service';

export interface ClientInvitation {
  id: string;
  token: string;
  email: string;
  role: string;
  tenantId: string;
  contractId: string;
  registrationLinkMethod: 'Email' | 'WhatsApp';
  created_at: Date;
  status: 'pending' | 'accepted' | 'expired';
  adminId?: string;
  managerId?: string;
}

@Injectable({
  providedIn: 'root',
})
export class ClientInvitationService {
  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private emailService: EmailService
  ) {}

  /**
   * Generate a random token for invitation
   */
  private generateToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }

  /**
   * Create and send client invitation
   */
  async createClientInvitation(
    email: string,
    tenantId: string,
    contractId: string,
    registrationLinkMethod: 'Email' | 'WhatsApp'
  ): Promise<string> {
    const user = await this.afAuth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if user already exists
    const usersSnapshot = await this.afs
      .collection('users', (ref) => ref.where('email', '==', email).limit(1))
      .get()
      .toPromise();

    if (!usersSnapshot?.empty) {
      throw new Error('User already exists and cannot be invited again.');
    }

    // Note: We don't check for existing tenants here because the tenant record
    // is created during contract finalization, and we want to allow the client
    // to register as a user even though they exist as a tenant.
    // The tenant and user records serve different purposes and can coexist.

    // Check for existing invitations
    const invitesSnapshot = await this.afs
      .collection('invites', (ref) => ref.where('email', '==', email).limit(1))
      .get()
      .toPromise();

    if (!invitesSnapshot?.empty) {
      throw new Error(
        'An invitation has already been sent to this email address.'
      );
    }

    // Get current user data for admin/manager IDs
    const userDoc = await this.afs
      .collection('users')
      .doc(user.uid)
      .get()
      .toPromise();

    if (!userDoc?.exists) {
      throw new Error('Your user profile was not found');
    }

    const userData = userDoc.data() as any;

    // Create new invitation
    const token = this.generateToken();
    const invitationId = this.afs.createId();
    const invitationData: ClientInvitation = {
      id: invitationId,
      email: email,
      role: 'client',
      token: token,
      tenantId: tenantId,
      contractId: contractId,
      registrationLinkMethod: registrationLinkMethod,
      created_at: new Date(),
      status: 'pending',
      // Set admin and manager IDs based on current user's role
      ...(userData.role === 'owner'
        ? { adminId: user.uid, managerId: user.uid }
        : {
            managerId: user.uid,
            adminId: userData.adminId || '',
          }),
    };

    // Save invitation to Firestore
    await this.afs.collection('invites').doc(invitationId).set(invitationData);

    // Generate registration link
    const registrationLink = `${window.location.origin}/create-account/${token}`;

    // Send invitation based on selected method
    await this.sendInvitation(email, registrationLink, registrationLinkMethod);

    return invitationId;
  }

  /**
   * Send invitation via selected method
   */
  private async sendInvitation(
    email: string,
    registrationLink: string,
    method: 'Email' | 'WhatsApp'
  ): Promise<void> {
    if (method === 'Email') {
      const success = await this.emailService.sendInvitationEmail(
        email,
        registrationLink,
        'client'
      );
      if (!success) {
        throw new Error('Failed to send invitation email');
      }
    } else if (method === 'WhatsApp') {
      // For now, just console log the WhatsApp message
      // In the future, this would integrate with WhatsApp Business API
      this.sendWhatsAppInvitation(email, registrationLink);
    }
  }

  /**
   * Send WhatsApp invitation (currently console.log)
   */
  private sendWhatsAppInvitation(
    email: string,
    registrationLink: string
  ): void {
    const whatsappMessage = `
🏢 Welcome to HubCenter!

You've been invited to create your client account.

📧 Email: ${email}
🔗 Registration Link: ${registrationLink}

Please click the link above to complete your registration and access your client portal.

Thank you for choosing HubCenter!
    `.trim();

    console.log('=== WHATSAPP INVITATION ===');
    console.log('Recipient Email:', email);
    console.log('Registration Link:', registrationLink);
    console.log('WhatsApp Message:');
    console.log(whatsappMessage);
    console.log('=== END WHATSAPP INVITATION ===');

    // TODO: Integrate with WhatsApp Business API
    // Example structure for future implementation:
    // await this.whatsappService.sendMessage(phoneNumber, whatsappMessage);
  }

  /**
   * Resend invitation
   */
  async resendInvitation(invitationId: string): Promise<void> {
    const inviteDoc = await this.afs
      .collection('invites')
      .doc(invitationId)
      .get()
      .toPromise();

    if (!inviteDoc?.exists) {
      throw new Error('Invitation not found');
    }

    const invitation = inviteDoc.data() as ClientInvitation;
    const registrationLink = `${window.location.origin}/create-account/${invitation.token}`;

    await this.sendInvitation(
      invitation.email,
      registrationLink,
      invitation.registrationLinkMethod
    );
  }

  /**
   * Get all client invitations
   */
  getClientInvitations() {
    return this.afs
      .collection<ClientInvitation>('invites', (ref) =>
        ref.where('role', '==', 'client').orderBy('created_at', 'desc')
      )
      .valueChanges({ idField: 'id' });
  }

  /**
   * Update invitation status
   */
  async updateInvitationStatus(
    invitationId: string,
    status: 'pending' | 'accepted' | 'expired'
  ): Promise<void> {
    await this.afs.collection('invites').doc(invitationId).update({
      status: status,
      updatedAt: new Date(),
    });
  }
}
