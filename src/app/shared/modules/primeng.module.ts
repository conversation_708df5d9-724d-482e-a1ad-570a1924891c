import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { CardModule } from 'primeng/card';
import { PanelMenuModule } from 'primeng/panelmenu';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { MenubarModule } from 'primeng/menubar';
import { ToolbarModule } from 'primeng/toolbar';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { SelectButtonModule } from 'primeng/selectbutton';
import { InputNumberModule } from 'primeng/inputnumber';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { AccordionModule } from 'primeng/accordion';
import { ImageModule } from 'primeng/image';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { InputMaskModule } from 'primeng/inputmask';
import { FileUploadModule } from 'primeng/fileupload';
import { SidebarModule } from 'primeng/sidebar';
import { ChipModule } from 'primeng/chip';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { BadgeModule } from 'primeng/badge';
import { StepsModule } from 'primeng/steps';
import { CheckboxModule } from 'primeng/checkbox';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TimelineModule } from 'primeng/timeline';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,

    DividerModule,
    ButtonModule,
    InputTextModule,
    CardModule,
    PanelMenuModule,
    BreadcrumbModule,
    MenubarModule,
    ToolbarModule,
    TableModule,
    ConfirmPopupModule,
    ToastModule,
    DropdownModule,
    SelectButtonModule,
    InputNumberModule,
    OverlayPanelModule,
    AccordionModule,
    ImageModule,
    CalendarModule,
    DialogModule,
    InputMaskModule,
    FileUploadModule,
    SidebarModule,
    ChipModule,
    InputTextareaModule,
    BadgeModule,
    StepsModule,
    CheckboxModule,
    RadioButtonModule,
    TimelineModule,
  ],
  exports: [
    FormsModule,
    ReactiveFormsModule,

    DividerModule,
    ButtonModule,
    InputTextModule,
    CardModule,
    PanelMenuModule,
    BreadcrumbModule,
    MenubarModule,
    ToolbarModule,
    TableModule,
    ConfirmPopupModule,
    ToastModule,
    DropdownModule,
    SelectButtonModule,
    InputNumberModule,
    OverlayPanelModule,
    AccordionModule,
    ImageModule,
    CalendarModule,
    DialogModule,
    InputMaskModule,
    FileUploadModule,
    SidebarModule,
    ChipModule,
    InputTextareaModule,
    BadgeModule,
    StepsModule,
    CheckboxModule,
    RadioButtonModule,
    TimelineModule,
  ],
})
export class PrimengModule {}
